<?php $__env->startSection('title', 'UangTix - Dompet Digital'); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* UangTix OVO/GoPay Style */
.uangtix-container {
    background: linear-gradient(180deg, #4f46e5 0%, #6366f1 100%);
    min-height: 100vh;
    padding: 0;
}

.wallet-header {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    padding: 20px 16px 40px;
    position: relative;
    overflow: hidden;
}

.wallet-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
}

.wallet-header::after {
    content: '';
    position: absolute;
    bottom: -30%;
    left: -10%;
    width: 150px;
    height: 150px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 50%;
}

.balance-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 24px;
    margin: 0 16px;
    position: relative;
    z-index: 10;
}

.balance-amount {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.balance-label {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.875rem;
    margin-bottom: 8px;
}

.balance-idr {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1rem;
    margin-top: 4px;
}

.main-actions {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    padding: 24px 16px;
    background: white;
    margin-top: -20px;
    border-radius: 20px 20px 0 0;
    position: relative;
    z-index: 5;
}

.action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 12px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #f1f5f9;
    transition: all 0.3s ease;
    cursor: pointer;
    text-decoration: none;
    color: inherit;
}

.action-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    text-decoration: none;
    color: inherit;
}

.action-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;
    position: relative;
}

.action-icon.deposit {
    background: linear-gradient(135deg, #10b981, #059669);
}

.action-icon.withdraw {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.action-icon.transfer {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.action-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #1f2937;
    text-align: center;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    padding: 0 16px 24px;
    background: white;
}

.stat-card {
    background: #f8fafc;
    border-radius: 12px;
    padding: 16px;
    border: 1px solid #e2e8f0;
}

.stat-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 0.75rem;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.transactions-section {
    background: white;
    padding: 24px 16px;
    min-height: 400px;
}

.section-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 20px;
}

.section-title {
    font-size: 1.125rem;
    font-weight: 700;
    color: #1f2937;
}

.view-all-btn {
    font-size: 0.875rem;
    color: #4f46e5;
    font-weight: 600;
    text-decoration: none;
}

.transaction-item {
    display: flex;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #f1f5f9;
}

.transaction-item:last-child {
    border-bottom: none;
}

.transaction-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
}

.transaction-icon.deposit {
    background: #dcfce7;
    color: #16a34a;
}

.transaction-icon.withdrawal {
    background: #fef3c7;
    color: #d97706;
}

.transaction-icon.transfer_in {
    background: #dbeafe;
    color: #2563eb;
}

.transaction-icon.transfer_out {
    background: #fce7f3;
    color: #be185d;
}

.transaction-details {
    flex: 1;
}

.transaction-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 2px;
}

.transaction-date {
    font-size: 0.75rem;
    color: #64748b;
}

.transaction-amount {
    font-size: 0.875rem;
    font-weight: 700;
    text-align: right;
}

.transaction-amount.positive {
    color: #16a34a;
}

.transaction-amount.negative {
    color: #dc2626;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #64748b;
}

.empty-icon {
    width: 64px;
    height: 64px;
    background: #f1f5f9;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
}

/* Modal Styles - Clean and Modern */
.modal-content {
    border: none;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    overflow: hidden;
}

.modal-header {
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    padding: 20px 24px;
}

.modal-title {
    font-size: 1.125rem;
    font-weight: 700;
    color: #1f2937;
    display: flex;
    align-items: center;
    gap: 8px;
}

.modal-body {
    padding: 24px;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    background: white;
}

.form-control:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-text {
    font-size: 0.75rem;
    color: #6b7280;
    margin-top: 4px;
}

.summary-card {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 16px;
    margin: 20px 0;
}

.summary-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 12px;
}

.summary-row {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 8px;
}

.summary-row:last-child {
    margin-bottom: 0;
    padding-top: 8px;
    border-top: 1px solid #e2e8f0;
    font-weight: 600;
}

.summary-label {
    font-size: 0.875rem;
    color: #6b7280;
}

.summary-value {
    font-size: 0.875rem;
    color: #1f2937;
    font-weight: 500;
}

.btn-group {
    display: flex;
    gap: 12px;
    margin-top: 24px;
}

.btn {
    flex: 1;
    padding: 12px 24px;
    border-radius: 12px;
    font-size: 0.875rem;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-primary {
    background: #4f46e5;
    color: white;
}

.btn-primary:hover {
    background: #4338ca;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #f1f5f9;
    color: #64748b;
    border: 1px solid #e2e8f0;
}

.btn-secondary:hover {
    background: #e2e8f0;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .balance-amount {
        font-size: 2rem;
    }

    .main-actions {
        padding: 20px 12px;
    }

    .action-item {
        padding: 16px 8px;
    }

    .action-icon {
        width: 40px;
        height: 40px;
    }

    .modal-body {
        padding: 20px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 8px;
    }
}

/* PWA Styles */
@media (display-mode: standalone) {
    .wallet-header {
        padding-top: 60px; /* Account for status bar */
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .main-actions,
    .stats-section,
    .transactions-section {
        background: #1f2937;
    }

    .action-item,
    .stat-card {
        background: #374151;
        border-color: #4b5563;
    }

    .action-label,
    .stat-value,
    .section-title,
    .transaction-title {
        color: #f9fafb;
    }

    .stat-label,
    .transaction-date {
        color: #9ca3af;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="uangtix-container">
    <!-- Wallet Header -->
    <div class="wallet-header">
        <div class="balance-card">
            <div class="balance-label">Saldo UangTix</div>
            <div class="balance-amount">
                <?php echo e(number_format($balance->balance, 0, ',', '.')); ?> <span style="font-size: 1.5rem; opacity: 0.8;">UTX</span>
            </div>
            <div class="balance-idr">
                ≈ Rp <?php echo e(number_format($balance->balance * $exchangeRate->rate_uangtix_to_idr, 0, ',', '.')); ?>

            </div>
        </div>
    </div>

    <!-- Main Actions -->
    <div class="main-actions">
        <button class="action-item" data-bs-toggle="modal" data-bs-target="#depositModal">
            <div class="action-icon deposit">
                <i data-lucide="plus" class="w-6 h-6 text-white"></i>
            </div>
            <div class="action-label">Deposit</div>
        </button>

        <button class="action-item" data-bs-toggle="modal" data-bs-target="#withdrawModal">
            <div class="action-icon withdraw">
                <i data-lucide="minus" class="w-6 h-6 text-white"></i>
            </div>
            <div class="action-label">Tarik</div>
        </button>

        <button class="action-item" data-bs-toggle="modal" data-bs-target="#transferModal">
            <div class="action-icon transfer">
                <i data-lucide="send" class="w-6 h-6 text-white"></i>
            </div>
            <div class="action-label">Transfer</div>
        </button>
    </div>

    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value"><?php echo e(number_format($stats['total_earned'], 0, ',', '.')); ?></div>
            <div class="stat-label">Total Earned</div>
        </div>
        <div class="stat-card">
            <div class="stat-value"><?php echo e(number_format($stats['total_deposited'], 0, ',', '.')); ?></div>
            <div class="stat-label">Total Deposit</div>
        </div>
        <div class="stat-card">
            <div class="stat-value"><?php echo e(number_format($stats['total_withdrawn'], 0, ',', '.')); ?></div>
            <div class="stat-label">Total Tarik</div>
        </div>
        <div class="stat-card">
            <div class="stat-value"><?php echo e($stats['transactions_this_month']); ?></div>
            <div class="stat-label">Transaksi Bulan Ini</div>
        </div>
    </div>

    <!-- Recent Transactions -->
    <div class="transactions-section">
        <div class="section-header">
            <div class="section-title">Transaksi Terbaru</div>
            <a href="<?php echo e(route('uangtix.transactions')); ?>" class="view-all-btn">Lihat Semua</a>
        </div>

        <?php if($recentTransactions->count() > 0): ?>
            <?php $__currentLoopData = $recentTransactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="transaction-item">
                <div class="transaction-icon <?php echo e($transaction->type); ?>">
                    <?php if($transaction->type === 'deposit'): ?>
                        <i data-lucide="arrow-down" class="w-5 h-5"></i>
                    <?php elseif($transaction->type === 'withdrawal'): ?>
                        <i data-lucide="arrow-up" class="w-5 h-5"></i>
                    <?php elseif($transaction->type === 'transfer_in'): ?>
                        <i data-lucide="arrow-down-left" class="w-5 h-5"></i>
                    <?php elseif($transaction->type === 'transfer_out'): ?>
                        <i data-lucide="arrow-up-right" class="w-5 h-5"></i>
                    <?php else: ?>
                        <i data-lucide="activity" class="w-5 h-5"></i>
                    <?php endif; ?>
                </div>
                <div class="transaction-details">
                    <div class="transaction-title">
                        <?php if($transaction->type === 'deposit'): ?> Deposit
                        <?php elseif($transaction->type === 'withdrawal'): ?> Penarikan
                        <?php elseif($transaction->type === 'transfer_in'): ?> Transfer Masuk
                        <?php elseif($transaction->type === 'transfer_out'): ?> Transfer Keluar
                        <?php else: ?> <?php echo e(ucfirst($transaction->type)); ?>

                        <?php endif; ?>
                    </div>
                    <div class="transaction-date"><?php echo e($transaction->created_at->format('d M Y, H:i')); ?></div>
                </div>
                <div class="transaction-amount <?php echo e(in_array($transaction->type, ['deposit', 'transfer_in', 'earning']) ? 'positive' : 'negative'); ?>">
                    <?php if(in_array($transaction->type, ['deposit', 'transfer_in', 'earning'])): ?>
                        +<?php echo e(number_format($transaction->amount, 0, ',', '.')); ?>

                    <?php else: ?>
                        -<?php echo e(number_format(abs($transaction->amount), 0, ',', '.')); ?>

                    <?php endif; ?>
                    UTX
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php else: ?>
            <div class="empty-state">
                <div class="empty-icon">
                    <i data-lucide="activity" class="w-8 h-8 text-gray-400"></i>
                </div>
                <div style="font-weight: 600; margin-bottom: 8px;">Belum ada transaksi</div>
                <div>Mulai dengan melakukan deposit pertama Anda</div>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Deposit Modal -->
<div class="modal fade" id="depositModal" tabindex="-1" aria-labelledby="depositModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">
                    <i data-lucide="plus" class="w-5 h-5 text-green-600"></i>
                    Deposit UangTix
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="depositForm">
                    <?php echo csrf_field(); ?>
                    <div class="form-group">
                        <label for="depositAmount" class="form-label">Jumlah Deposit (IDR)</label>
                        <input type="number" class="form-control" id="depositAmount" name="amount_idr"
                               min="<?php echo e($exchangeRate->min_deposit_idr); ?>"
                               max="<?php echo e($exchangeRate->max_deposit_idr); ?>"
                               placeholder="Masukkan jumlah dalam Rupiah" required>
                        <div class="form-text">
                            Minimum: Rp <?php echo e(number_format($exchangeRate->min_deposit_idr, 0, ',', '.')); ?> -
                            Maximum: Rp <?php echo e(number_format($exchangeRate->max_deposit_idr, 0, ',', '.')); ?>

                        </div>
                    </div>

                    <div class="form-group">
                        <label for="paymentMethod" class="form-label">Metode Pembayaran</label>
                        <select class="form-control" id="paymentMethod" name="payment_method" required>
                            <option value="">Pilih metode pembayaran</option>
                            <option value="bank_transfer">Transfer Bank</option>
                            <option value="virtual_account">Virtual Account</option>
                            <option value="e_wallet">E-Wallet</option>
                            <option value="qris">QRIS</option>
                        </select>
                    </div>

                    <div class="summary-card">
                        <div class="summary-title">Ringkasan Deposit</div>
                        <div class="summary-row">
                            <span class="summary-label">Jumlah Deposit:</span>
                            <span class="summary-value" id="depositSummaryAmount">Rp 0</span>
                        </div>
                        <div class="summary-row">
                            <span class="summary-label">Fee (<?php echo e($exchangeRate->deposit_fee_percentage); ?>%):</span>
                            <span class="summary-value" id="depositSummaryFee">Rp 0</span>
                        </div>
                        <div class="summary-row">
                            <span class="summary-label">Jumlah Bersih:</span>
                            <span class="summary-value" id="depositSummaryNet">Rp 0</span>
                        </div>
                        <div class="summary-row">
                            <span class="summary-label">UangTix yang Diterima:</span>
                            <span class="summary-value" id="depositSummaryUTX" style="color: #10b981; font-weight: 700;">0 UTX</span>
                        </div>
                    </div>

                    <div class="btn-group">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-primary">Proses Deposit</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Withdraw Modal -->
<div class="modal fade" id="withdrawModal" tabindex="-1" aria-labelledby="withdrawModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">
                    <i data-lucide="minus" class="w-5 h-5 text-orange-600"></i>
                    Tarik Saldo UangTix
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="withdrawForm">
                    <?php echo csrf_field(); ?>
                    <div class="form-group">
                        <label for="withdrawAmount" class="form-label">Jumlah Penarikan (UTX)</label>
                        <input type="number" class="form-control" id="withdrawAmount" name="amount_uangtix"
                               min="<?php echo e($exchangeRate->min_withdrawal_uangtix); ?>"
                               max="<?php echo e($balance->balance); ?>"
                               placeholder="Masukkan jumlah UangTix" required>
                        <div class="form-text">
                            Saldo tersedia: <?php echo e(number_format($balance->balance, 0, ',', '.')); ?> UTX -
                            Minimum: <?php echo e(number_format($exchangeRate->min_withdrawal_uangtix, 0, ',', '.')); ?> UTX
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="bankName" class="form-label">Nama Bank</label>
                        <select class="form-control" id="bankName" name="bank_name" required>
                            <option value="">Pilih Bank</option>
                            <option value="BCA">BCA</option>
                            <option value="BNI">BNI</option>
                            <option value="BRI">BRI</option>
                            <option value="Mandiri">Mandiri</option>
                            <option value="CIMB">CIMB Niaga</option>
                            <option value="Danamon">Danamon</option>
                            <option value="Permata">Permata</option>
                            <option value="BTN">BTN</option>
                            <option value="BSI">BSI</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="bankAccountNumber" class="form-label">Nomor Rekening</label>
                        <input type="text" class="form-control" id="bankAccountNumber" name="bank_account_number"
                               placeholder="Nomor rekening" required>
                    </div>

                    <div class="form-group">
                        <label for="bankAccountName" class="form-label">Nama Pemilik Rekening</label>
                        <input type="text" class="form-control" id="bankAccountName" name="bank_account_name"
                               placeholder="Nama sesuai rekening bank" required>
                    </div>

                    <div class="summary-card">
                        <div class="summary-title">Ringkasan Penarikan</div>
                        <div class="summary-row">
                            <span class="summary-label">Jumlah Penarikan:</span>
                            <span class="summary-value" id="withdrawSummaryAmount">0 UTX</span>
                        </div>
                        <div class="summary-row">
                            <span class="summary-label">Fee (<?php echo e($exchangeRate->withdrawal_fee_percentage); ?>%):</span>
                            <span class="summary-value" id="withdrawSummaryFee">0 UTX</span>
                        </div>
                        <div class="summary-row">
                            <span class="summary-label">Diterima di Rekening:</span>
                            <span class="summary-value" id="withdrawSummaryNet" style="color: #f59e0b; font-weight: 700;">Rp 0</span>
                        </div>
                    </div>

                    <div class="btn-group">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-primary" style="background: #f59e0b;">Proses Penarikan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Transfer Modal -->
<div class="modal fade" id="transferModal" tabindex="-1" aria-labelledby="transferModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">
                    <i data-lucide="send" class="w-5 h-5 text-blue-600"></i>
                    Transfer UangTix
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="transferForm">
                    <?php echo csrf_field(); ?>
                    <div class="form-group">
                        <label for="toUserEmail" class="form-label">Email Penerima</label>
                        <input type="email" class="form-control" id="toUserEmail" name="to_user_email"
                               placeholder="Masukkan email penerima" required>
                        <div class="form-text">Pastikan email penerima terdaftar di TiXara</div>
                    </div>

                    <div class="form-group">
                        <label for="transferAmount" class="form-label">Jumlah Transfer (UTX)</label>
                        <input type="number" class="form-control" id="transferAmount" name="amount"
                               min="1" max="<?php echo e($balance->balance); ?>"
                               placeholder="Masukkan jumlah UangTix" required>
                        <div class="form-text">
                            Saldo tersedia: <?php echo e(number_format($balance->balance, 0, ',', '.')); ?> UTX
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="transferDescription" class="form-label">Keterangan (Opsional)</label>
                        <textarea class="form-control" id="transferDescription" name="description"
                                  rows="3" placeholder="Keterangan transfer..."></textarea>
                    </div>

                    <div class="summary-card">
                        <div class="summary-title">Ringkasan Transfer</div>
                        <div class="summary-row">
                            <span class="summary-label">Jumlah Transfer:</span>
                            <span class="summary-value" id="transferSummaryAmount">0 UTX</span>
                        </div>
                        <div class="summary-row">
                            <span class="summary-label">Biaya Transfer:</span>
                            <span class="summary-value" id="transferSummaryFee">0 UTX (Gratis)</span>
                        </div>
                        <div class="summary-row">
                            <span class="summary-label">Total yang Dikirim:</span>
                            <span class="summary-value" id="transferSummaryTotal" style="color: #3b82f6; font-weight: 700;">0 UTX</span>
                        </div>
                    </div>

                    <div class="btn-group">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-primary" style="background: #3b82f6;">Kirim Transfer</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
/*
 * UangTix JavaScript
 *
 * Copyright (c) 2024 BintangCode
 * Sub Holding CV Bintang Gumilang Group
 *
 * Developer: Dhafa Nazula P
 * Instagram: @seehai.dhafa
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize Lucide icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }

    // Deposit form handling
    document.getElementById('depositForm').addEventListener('submit', async function(e) {
        e.preventDefault();

        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Memproses...';

        const formData = new FormData(this);

        try {
            const response = await fetch('<?php echo e(route("uangtix.deposit")); ?>', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: formData
            });

            const data = await response.json();

            if (data.success) {
                showToast('success', data.message);
                bootstrap.Modal.getInstance(document.getElementById('depositModal')).hide();
                setTimeout(() => location.reload(), 1500);
            } else {
                showToast('error', data.message || 'Terjadi kesalahan saat memproses deposit');
            }
        } catch (error) {
            console.error('Deposit error:', error);
            showToast('error', 'Terjadi kesalahan jaringan. Silakan coba lagi.');
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        }
    });

    // Withdraw form handling
    document.getElementById('withdrawForm').addEventListener('submit', async function(e) {
        e.preventDefault();

        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Memproses...';

        const formData = new FormData(this);

        try {
            const response = await fetch('<?php echo e(route("uangtix.withdraw")); ?>', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: formData
            });

            const data = await response.json();

            if (data.success) {
                showToast('success', data.message);
                bootstrap.Modal.getInstance(document.getElementById('withdrawModal')).hide();
                setTimeout(() => location.reload(), 1500);
            } else {
                showToast('error', data.message || 'Terjadi kesalahan saat memproses penarikan');
            }
        } catch (error) {
            console.error('Withdraw error:', error);
            showToast('error', 'Terjadi kesalahan jaringan. Silakan coba lagi.');
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        }
    });

    // Transfer form handling
    document.getElementById('transferForm').addEventListener('submit', async function(e) {
        e.preventDefault();

        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Memproses...';

        const formData = new FormData(this);

        try {
            const response = await fetch('<?php echo e(route("uangtix.transfer")); ?>', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: formData
            });

            const data = await response.json();

            if (data.success) {
                showToast('success', data.message);
                bootstrap.Modal.getInstance(document.getElementById('transferModal')).hide();
                setTimeout(() => location.reload(), 1500);
            } else {
                showToast('error', data.message || 'Terjadi kesalahan saat memproses transfer');
            }
        } catch (error) {
            console.error('Transfer error:', error);
            showToast('error', 'Terjadi kesalahan jaringan. Silakan coba lagi.');
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        }
    });

    // Real-time calculation for deposit
    document.getElementById('depositAmount').addEventListener('input', function() {
        const amount = parseFloat(this.value) || 0;
        const exchangeRate = <?php echo e($exchangeRate->rate_idr_to_uangtix ?? 0.001); ?>;
        const feePercentage = <?php echo e($exchangeRate->deposit_fee_percentage ?? 2.5); ?>;
        const minDeposit = <?php echo e($exchangeRate->min_deposit_idr ?? 10000); ?>;

        if (amount >= minDeposit) {
            const fee = amount * (feePercentage / 100);
            const netAmount = amount - fee;
            const uangTixAmount = netAmount * exchangeRate;

            document.getElementById('depositSummaryAmount').textContent = `Rp ${amount.toLocaleString('id-ID')}`;
            document.getElementById('depositSummaryFee').textContent = `Rp ${fee.toLocaleString('id-ID')}`;
            document.getElementById('depositSummaryNet').textContent = `Rp ${netAmount.toLocaleString('id-ID')}`;
            document.getElementById('depositSummaryUTX').textContent = `${Math.floor(uangTixAmount).toLocaleString('id-ID')} UTX`;
        } else {
            document.getElementById('depositSummaryAmount').textContent = 'Rp 0';
            document.getElementById('depositSummaryFee').textContent = 'Rp 0';
            document.getElementById('depositSummaryNet').textContent = 'Rp 0';
            document.getElementById('depositSummaryUTX').textContent = '0 UTX';
        }
    });

    // Real-time calculation for withdrawal
    document.getElementById('withdrawAmount').addEventListener('input', function() {
        const amount = parseFloat(this.value) || 0;
        const exchangeRate = <?php echo e($exchangeRate->rate_uangtix_to_idr ?? 1000); ?>;
        const feePercentage = <?php echo e($exchangeRate->withdrawal_fee_percentage ?? 2.5); ?>;
        const minWithdrawal = <?php echo e($exchangeRate->min_withdrawal_uangtix ?? 10); ?>;

        if (amount >= minWithdrawal) {
            const grossIdr = amount * exchangeRate;
            const fee = amount * (feePercentage / 100);
            const netIdr = (amount - fee) * exchangeRate;

            document.getElementById('withdrawSummaryAmount').textContent = `${amount.toLocaleString('id-ID')} UTX`;
            document.getElementById('withdrawSummaryFee').textContent = `${fee.toLocaleString('id-ID')} UTX`;
            document.getElementById('withdrawSummaryNet').textContent = `Rp ${Math.floor(netIdr).toLocaleString('id-ID')}`;
        } else {
            document.getElementById('withdrawSummaryAmount').textContent = '0 UTX';
            document.getElementById('withdrawSummaryFee').textContent = '0 UTX';
            document.getElementById('withdrawSummaryNet').textContent = 'Rp 0';
        }
    });

    // Real-time calculation for transfer
    document.getElementById('transferAmount').addEventListener('input', function() {
        const amount = parseFloat(this.value) || 0;

        document.getElementById('transferSummaryAmount').textContent = `${amount.toLocaleString('id-ID')} UTX`;
        document.getElementById('transferSummaryTotal').textContent = `${amount.toLocaleString('id-ID')} UTX`;
    });

    // Reset forms when modals are hidden
    document.getElementById('depositModal').addEventListener('hidden.bs.modal', function() {
        document.getElementById('depositForm').reset();
        document.getElementById('depositSummaryAmount').textContent = 'Rp 0';
        document.getElementById('depositSummaryFee').textContent = 'Rp 0';
        document.getElementById('depositSummaryNet').textContent = 'Rp 0';
        document.getElementById('depositSummaryUTX').textContent = '0 UTX';
    });

    document.getElementById('withdrawModal').addEventListener('hidden.bs.modal', function() {
        document.getElementById('withdrawForm').reset();
        document.getElementById('withdrawSummaryAmount').textContent = '0 UTX';
        document.getElementById('withdrawSummaryFee').textContent = '0 UTX';
        document.getElementById('withdrawSummaryNet').textContent = 'Rp 0';
    });

    document.getElementById('transferModal').addEventListener('hidden.bs.modal', function() {
        document.getElementById('transferForm').reset();
        document.getElementById('transferSummaryAmount').textContent = '0 UTX';
        document.getElementById('transferSummaryTotal').textContent = '0 UTX';
    });
});

function showToast(type, message) {
    // Remove existing toasts
    document.querySelectorAll('.toast-notification').forEach(toast => toast.remove());

    // Create new toast
    const toast = document.createElement('div');
    toast.className = `toast-notification alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; max-width: 400px;';
    toast.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
            <div class="flex-grow-1">${message}</div>
            <button type="button" class="btn-close" onclick="this.parentElement.parentElement.remove()"></button>
        </div>
    `;

    document.body.appendChild(toast);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.remove();
        }
    }, 5000);
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\Project-tixara.my.id\resources\views/pages/uangtix/index.blade.php ENDPATH**/ ?>